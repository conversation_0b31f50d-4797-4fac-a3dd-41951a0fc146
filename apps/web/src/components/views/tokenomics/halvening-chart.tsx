'use client';

import { useCallback, useMemo, useState } from 'react';
import { Annotation, LineSubject, Label, HtmlLabel } from '@visx/annotation';
import { LinearGradient } from '@visx/gradient';
import { ParentSize } from '@visx/responsive';
import { scaleTime } from '@visx/scale';
import {
  AreaSeries,
  Axis,
  Tooltip,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import * as d3 from 'd3';
import { utc } from 'moment';
import type {
  HalveningData,
  TokenomicCardProps,
} from '@repo/types/website-api-types';
import { Text } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { TokenomicsCard } from './tokenomics-card';
import type { DataPoint } from '@/components/views/subnets/registration-cost-chart';

const accessors = {
  xAccessor: (d: DataPoint) => new Date(d.x),
  yAccessor: (d: DataPoint) => d.y,
};

export default function HalveningChart({ data }: { data: HalveningData[] }) {
  const { isMobile } = useWindowSize();
  const [selectedIndex, setSelectedIndex] = useState<number>(1);

  const metaCards = useMemo<TokenomicCardProps[]>(() => {
    const selectedData = data[selectedIndex];

    return [
      {
        header: 'Total in Circulation (T)',
        content: Number(selectedData.total.replaceAll(',', '')).toLocaleString(
          'en-US',
          {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }
        ),
        variant: 'text',
      },
      {
        header: 'Block Reward (TAO)',
        content: selectedData.reward,
        variant: 'text',
      },
      {
        header: 'Duration (Blocks)',
        content: selectedData.blocks.toLocaleString(),
        variant: 'text',
      },
      {
        header: 'Issued this H (TAO)',
        content: Number(selectedData.issue.replaceAll(',', '')).toLocaleString(
          'en-US',
          {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }
        ),
        variant: 'text',
      },
    ];
  }, [data, selectedIndex]);

  const halveningData = useMemo(() => {
    const final = data.map((item) => ({
      x: utc(item.time * 1000)
        .toDate()
        .toISOString(),
      y: Number(item.total.replaceAll(',', '')),
    }));

    const initial = [
      {
        x: new Date('2021-01-03').toISOString(),
        y: 0,
      },
      {
        x: new Date('2021-05-15').toISOString(),
        y: 546113,
      },
      {
        x: new Date('2021-11-02').toISOString(),
        y: 546113,
      },
      // {
      // 	x: new Date("2023-03-20").toISOString(),
      // 	y: undefined,
      // },
    ];

    return [...initial, ...final];
  }, [data]);

  const getDaySuffix = useCallback((date: number) => {
    if (date > 3 && date < 21) return 'th';
    switch (date % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }, []);

  const customTheme = buildChartTheme({
    backgroundColor: '',
    colors: ['#EB5347'],
    gridColor: '#e8e8e8',
    gridColorDark: '#222831',
    tickLength: 1,
  });

  return (
    <div className='space-y-8 rounded-[10px] border border-[#95959533] bg-[#2E2E2E33] px-5 py-4 sm:px-10 sm:py-8'>
      <ParentSize className='mt-4 !h-[300px] !w-full'>
        {({ width }) => {
          const xDomain = d3.extent(halveningData, (d) => new Date(d.x));
          const xScale = scaleTime({
            range: [0, width],
            domain: xDomain[0] ? xDomain : undefined,
          });

          return (
            <XYChart
              height={300}
              width={width}
              xScale={{ type: 'time' }}
              yScale={{ type: 'linear' }}
              margin={{
                top: 0,
                right: 0,
                bottom: 40,
                left: 0,
              }}
              theme={customTheme}
            >
              <defs>
                <LinearGradient
                  id='gradient'
                  from='#EB5347'
                  fromOpacity={0.2}
                  to='#EB5347'
                  toOpacity={0}
                  x1='100%'
                  y1='0%'
                  x2='100%'
                  y2='100%'
                />
              </defs>
              <Axis
                hideAxisLine
                hideTicks
                orientation='bottom'
                tickFormat={(date: string) => {
                  return utc(date).format('YYYY');
                }}
                numTicks={isMobile ? 3 : 10}
                tickLabelProps={() => ({
                  fill: '#FFFFFF',
                  opacity: 0.4,
                  fontSize: 12,
                  fontWeight: 500,
                  fontFamily: 'var(--font-sans)',
                  dy: '10px',
                })}
              />
              <AreaSeries
                dataKey='Line 1'
                data={halveningData.slice()}
                yAccessor={accessors.yAccessor}
                xAccessor={accessors.xAccessor}
                fill='url(#gradient)'
                strokeWidth={0.8}
                lineProps={{
                  strokeWidth: 1,
                }}
              />
              {halveningData.map((datum, index) => {
                return (
                  <Annotation
                    key={index}
                    x={xScale(new Date(datum.x))}
                    y={datum.y}
                    dx={-10}
                    dy={-10}
                  >
                    <LineSubject
                      orientation='vertical'
                      stroke='#EB5347'
                      strokeWidth={1}
                      max={260}
                      min={0}
                      strokeDasharray='4 4'
                    />
                    <Label
                      backgroundFill='white'
                      title='Halvening Date'
                      subtitle=''
                      titleFontSize={14}
                      maxWidth={200}
                    />
                    <HtmlLabel
                      containerStyle={{
                        width: 300,
                        background: 'white',
                        border: '1px solid white',
                        borderRadius: 2,
                        color: 'red',
                        fontSize: '0.55em',
                        lineHeight: '1em',
                        padding: '0 0.4em 0 1em',
                        fontWeight: 200,
                        height: 100,
                      }}
                    >
                      Halvening Date
                    </HtmlLabel>
                  </Annotation>
                );
              })}
              <Tooltip
                snapTooltipToDatumX
                snapTooltipToDatumY
                showVerticalCrosshair
                showSeriesGlyphs
                verticalCrosshairStyle={{
                  stroke: '#EB5347',
                  strokeDasharray: '4 4',
                }}
                renderTooltip={({ tooltipData }) => {
                  if (!tooltipData || !tooltipData.nearestDatum) {
                    return null;
                  }

                  const { datum } = tooltipData.nearestDatum;
                  const halveningDatum = halveningData.findIndex(
                    (d: any) => d.x === (datum as DataPoint).x
                  );

                  if (!halveningDatum || halveningDatum <= 3) {
                    return null;
                  }

                  const currentNumber =
                    halveningDatum > 3 ? halveningDatum - 3 : 1;

                  if (setSelectedIndex) {
                    setSelectedIndex(currentNumber);
                  }

                  return (
                    <Text
                      level='base'
                      className='rounded-lg border border-neutral-800 bg-neutral-800/10 px-3.5 py-1 text-opacity-70 backdrop-blur-xl'
                    >
                      {`${currentNumber}${getDaySuffix(
                        currentNumber
                      )} Halvening`}
                    </Text>
                  );
                }}
              />
            </XYChart>
          );
        }}
      </ParentSize>
      <div className='flex flex-col gap-7'>
        <div className='flex flex-col gap-3'>
          <p className='sm:text-3xxl text-3xl'>
            {`${selectedIndex}${getDaySuffix(selectedIndex)}`} Halvening
          </p>
          <Text level='base' className='text-[#9E9E9E]'>
            Expected:{' '}
            {utc((data[selectedIndex]?.time ?? 0) * 1000).format(
              'DD MMMM YYYY'
            )}
          </Text>
        </div>
        <div className='flex flex-wrap gap-2'>
          {metaCards.map((item, index) => (
            <TokenomicsCard {...item} key={index} />
          ))}
        </div>
      </div>
    </div>
  );
}
